{"actions": [], "allow_rename": 1, "creation": "2025-07-28 22:42:47.838164", "doctype": "DocType", "engine": "InnoDB", "field_order": ["project", "area", "rate", "total_value", "column_break_nvqf", "date", "schedule_no", "section_break_rmqg", "table_xpyk", "section_break_byap", "total_amount", "column_break_kmxx", "total_percentage", "section_break_zrpv", "remarks"], "fields": [{"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}, {"fetch_from": "project.area", "fieldname": "area", "fieldtype": "Data", "label": "Area", "options": "Projects", "precision": "2"}, {"fetch_from": "project.rate", "fieldname": "rate", "fieldtype": "Data", "label": "Rate", "precision": "2"}, {"fetch_from": "project.total_value", "fieldname": "total_value", "fieldtype": "Data", "label": "Total Value", "options": "Projects"}, {"fieldname": "column_break_nvqf", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "schedule_no", "fieldtype": "Data", "label": "Schedule No"}, {"fieldname": "section_break_rmqg", "fieldtype": "Section Break"}, {"fieldname": "table_xpyk", "fieldtype": "Table", "options": "P Schedules Table"}, {"fieldname": "section_break_byap", "fieldtype": "Section Break"}, {"fieldname": "total_amount", "fieldtype": "Float", "label": "Total Amount"}, {"fieldname": "column_break_kmxx", "fieldtype": "Column Break"}, {"fieldname": "total_percentage", "fieldtype": "Float", "label": "Total Percentage"}, {"fieldname": "section_break_zrpv", "fieldtype": "Section Break"}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 22:42:47.838164", "modified_by": "Administrator", "module": "Projects", "name": "P Schedules", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}